<?php $__env->startSection('title', $service->title . ' - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Banner Slider -->
<section class="hero-section position-relative <?php echo e($banners->count() === 0 ? 'hero-fallback' : ''); ?>">
    <?php if($banners->count() > 0): ?>
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="carousel-item <?php echo e($index === 0 ? 'active' : ''); ?>">
                    <div class="banner-slide" style="background-image: url('<?php echo e(asset('storage/' . $banner->image_path)); ?>');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if($banners->count() > 1): ?>
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="<?php echo e($index); ?>"
                        class="<?php echo e($index === 0 ? 'active' : ''); ?>" aria-current="true" aria-label="Slide <?php echo e($index + 1); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb justify-content-center bg-transparent">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white">หน้าแรก</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('services')); ?>" class="text-white">บริการ</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e($service->title); ?></li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-4 text-white"><?php echo e($service->title); ?></h1>
                    <?php if($service->category): ?>
                    <div class="mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <?php if($service->category->icon): ?>
                            <i class="<?php echo e($service->category->icon); ?> me-2"></i>
                            <?php endif; ?>
                            <?php echo e($service->category->name); ?>

                        </span>
                    </div>
                    <?php endif; ?>
                    <p class="lead text-white"><?php echo e($service->description); ?></p>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb justify-content-center bg-transparent">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">หน้าแรก</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('services')); ?>">บริการ</a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo e($service->title); ?></li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-4"><?php echo e($service->title); ?></h1>
                    <?php if($service->category): ?>
                    <div class="mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <?php if($service->category->icon): ?>
                            <i class="<?php echo e($service->category->icon); ?> me-2"></i>
                            <?php endif; ?>
                            <?php echo e($service->category->name); ?>

                        </span>
                    </div>
                    <?php endif; ?>
                    <p class="lead"><?php echo e($service->description); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Service Detail Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body p-4">
                        <!-- Main Image -->
                        <div class="mb-4">
                            <?php
                                $mainImage = $service->images->where('is_cover', true)->first() ?? $service->images->first();
                                $mainImagePath = $mainImage ? $mainImage->image_path : $service->image;
                            ?>
                            <?php if($mainImagePath): ?>
                            <div class="img-container-fixed img-size-xlarge rounded shadow-sm">
                                <img src="<?php echo e(asset('storage/' . $mainImagePath)); ?>"
                                     class="img-fit-contain main-image"
                                     alt="<?php echo e($service->title); ?>"
                                     style="cursor: pointer;"
                                     data-bs-toggle="modal"
                                     data-bs-target="#imageModal"
                                     id="mainImage">
                            </div>
                            <?php else: ?>
                            <div class="bg-light d-flex align-items-center justify-content-center rounded shadow-sm" 
                                 style="height: 400px;">
                                <div class="text-center text-muted">
                                    <i class="fas fa-image fa-5x mb-3"></i>
                                    <p class="mb-0">ไม่มีรูปภาพ</p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Service Details -->
                        <?php if($service->details): ?>
                        <div class="mb-4">
                            <h3 class="fw-bold mb-3">รายละเอียดบริการ</h3>
                            <div class="service-details">
                                <?php echo nl2br(e($service->details)); ?>

                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Image Gallery -->
                        <?php if($service->images->count() > 1): ?>
                        <div class="mb-4">
                            <h3 class="fw-bold mb-3">แกลลอรี่รูปภาพ</h3>
                            <div class="row g-3">
                                <?php $__currentLoopData = $service->images->where('is_cover', false); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-4 col-sm-6">
                                    <div class="gallery-image-container img-size-thumbnail rounded shadow-sm">
                                        <img src="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                             class="img-fit-contain gallery-thumbnail"
                                             alt="<?php echo e($image->alt_text ?? $service->title); ?>"
                                             style="cursor: pointer;"
                                             data-bs-toggle="modal"
                                             data-bs-target="#imageModal"
                                             data-image="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                             data-alt="<?php echo e($image->alt_text ?? $service->title); ?>">
                                    </div>
                                    <?php if($image->description): ?>
                                    <small class="text-muted d-block mt-1"><?php echo e($image->description); ?></small>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Contact CTA -->
                        <div class="bg-light p-4 rounded">
                            <div class="text-center">
                                <h4 class="fw-bold mb-3">สนใจบริการนี้?</h4>
                                <p class="mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก</p>
                                <div class="d-flex justify-content-center gap-3 flex-wrap">
                                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                                    </a>
                                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                                    </a>
                                </div>
                                <small class="text-muted d-block mt-3">
                                    <i class="fas fa-clock me-1"></i>
                                    บริการตลอด 24 ชั่วโมง
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Service Category -->
                <?php if($service->category): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">หมวดหมู่บริการ</h5>
                        <div class="d-flex align-items-center">
                            <?php if($service->category->icon): ?>
                            <i class="<?php echo e($service->category->icon); ?> fa-2x me-3" style="color: <?php echo e($service->category->color); ?>"></i>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-1"><?php echo e($service->category->name); ?></h6>
                                <?php if($service->category->description): ?>
                                <small class="text-muted"><?php echo e($service->category->description); ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Related Services -->
                <?php if($relatedServices->count() > 0): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">บริการที่เกี่ยวข้อง</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php $__currentLoopData = $relatedServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border-bottom p-3">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    <?php
                                        $relatedImage = $relatedService->images->where('is_cover', true)->first() ?? $relatedService->images->first();
                                        $relatedImagePath = $relatedImage ? $relatedImage->image_path : $relatedService->image;
                                    ?>
                                    <?php if($relatedImagePath): ?>
                                    <img src="<?php echo e(asset('storage/' . $relatedImagePath)); ?>" 
                                         class="rounded" 
                                         style="width: 60px; height: 60px; object-fit: cover;"
                                         alt="<?php echo e($relatedService->title); ?>">
                                    <?php else: ?>
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="<?php echo e(route('services.show', $relatedService->id)); ?>" 
                                           class="text-decoration-none"><?php echo e($relatedService->title); ?></a>
                                    </h6>
                                    <p class="text-muted small mb-0"><?php echo e(Str::limit($relatedService->description, 80)); ?></p>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Contact Card -->
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">ต้องการความช่วยเหลือ?</h5>
                        <p class="card-text">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก</p>
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                            </a>
                            <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                            </a>
                        </div>
                        <small class="text-muted d-block mt-2">
                            <i class="fas fa-clock me-1"></i>
                            บริการตลอด 24 ชั่วโมง
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel"><?php echo e($service->title); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid rounded" alt="">
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Stable modal management
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('imageModalLabel');
    let modalInstance = null;

    function openModal(imageSrc, imageAlt) {
        // Clean up existing modal
        if (modalInstance) {
            modalInstance.dispose();
        }

        // Set image
        modalImage.src = imageSrc;
        modalImage.alt = imageAlt;

        // Create new modal
        modalInstance = new bootstrap.Modal(imageModal, {
            backdrop: true,
            keyboard: true,
            focus: true
        });

        modalInstance.show();

        // Add cleanup event
        imageModal.addEventListener('hidden.bs.modal', function() {
            cleanupServiceModal();
        }, { once: true });
    }

    function cleanupServiceModal() {
        // Clean up body
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('padding-right');
        document.body.style.removeProperty('overflow');

        // Remove backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        // Dispose modal instance
        if (modalInstance) {
            modalInstance.dispose();
            modalInstance = null;
        }
    }

    // Main image click
    const mainImage = document.getElementById('mainImage');
    if (mainImage) {
        mainImage.addEventListener('click', function() {
            openModal(this.src, this.alt);
        });
    }

    // Gallery thumbnails click
    const galleryThumbnails = document.querySelectorAll('.gallery-thumbnail');
    galleryThumbnails.forEach(function(thumbnail) {
        thumbnail.addEventListener('click', function() {
            openModal(this.dataset.image, this.dataset.alt);
        });
    });

    // Close button handler
    const closeBtn = imageModal.querySelector('.btn-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (modalInstance) {
                modalInstance.hide();
            } else {
                cleanupServiceModal();
            }
        });
    }

    // Backdrop click
    imageModal.addEventListener('click', function(e) {
        if (e.target === imageModal) {
            if (modalInstance) {
                modalInstance.hide();
            } else {
                cleanupServiceModal();
            }
        }
    });

    // Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && imageModal.classList.contains('show')) {
            if (modalInstance) {
                modalInstance.hide();
            } else {
                cleanupServiceModal();
            }
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/frontend/service-detail.blade.php ENDPATH**/ ?>