<?php $__env->startSection('title', $service->title . ' - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Banner Slider -->
<section class="hero-section position-relative <?php echo e($banners->count() === 0 ? 'hero-fallback' : ''); ?>">
    <?php if($banners->count() > 0): ?>
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="carousel-item <?php echo e($index === 0 ? 'active' : ''); ?>">
                    <div class="banner-slide" style="background-image: url('<?php echo e(asset('storage/' . $banner->image_path)); ?>');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if($banners->count() > 1): ?>
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="<?php echo e($index); ?>"
                        class="<?php echo e($index === 0 ? 'active' : ''); ?>" aria-current="true" aria-label="Slide <?php echo e($index + 1); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb justify-content-center bg-transparent">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white">หน้าแรก</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('services')); ?>" class="text-white">บริการ</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e($service->title); ?></li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-4 text-white"><?php echo e($service->title); ?></h1>
                    <?php if($service->category): ?>
                    <div class="mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <?php if($service->category->icon): ?>
                            <i class="<?php echo e($service->category->icon); ?> me-2"></i>
                            <?php endif; ?>
                            <?php echo e($service->category->name); ?>

                        </span>
                    </div>
                    <?php endif; ?>
                    <p class="lead text-white"><?php echo e($service->description); ?></p>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb justify-content-center bg-transparent">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">หน้าแรก</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('services')); ?>">บริการ</a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo e($service->title); ?></li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-4"><?php echo e($service->title); ?></h1>
                    <?php if($service->category): ?>
                    <div class="mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <?php if($service->category->icon): ?>
                            <i class="<?php echo e($service->category->icon); ?> me-2"></i>
                            <?php endif; ?>
                            <?php echo e($service->category->name); ?>

                        </span>
                    </div>
                    <?php endif; ?>
                    <p class="lead"><?php echo e($service->description); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Service Detail Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body p-4">
                        <!-- Main Image -->
                        <div class="mb-4">
                            <?php
                                $mainImage = $service->images->where('is_cover', true)->first() ?? $service->images->first();
                                $mainImagePath = $mainImage ? $mainImage->image_path : $service->image;
                            ?>
                            <?php if($mainImagePath): ?>
                            <div class="img-container-fixed img-size-xlarge rounded shadow-sm">
                                <img src="<?php echo e(asset('storage/' . $mainImagePath)); ?>"
                                     class="img-fit-contain main-image"
                                     alt="<?php echo e($service->title); ?>"
                                     style="cursor: pointer;"
                                     data-bs-toggle="modal"
                                     data-bs-target="#imageModal"
                                     id="mainImage">
                            </div>
                            <?php else: ?>
                            <div class="bg-light d-flex align-items-center justify-content-center rounded shadow-sm" 
                                 style="height: 400px;">
                                <div class="text-center text-muted">
                                    <i class="fas fa-image fa-5x mb-3"></i>
                                    <p class="mb-0">ไม่มีรูปภาพ</p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>



                        <!-- Image Gallery -->
                        <?php if($service->images->count() > 0): ?>
                        <div class="mb-4">
                            <h5 class="mb-3">แกลเลอรี่รูปภาพ</h5>
                            <div class="row g-2">
                                <?php $__currentLoopData = $service->images->sortBy('sort_order'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-3 col-4">
                                    <div class="gallery-image-container img-size-thumbnail">
                                        <img src="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                             class="img-fit-contain gallery-thumbnail"
                                             alt="<?php echo e($image->description ?? $service->title); ?>"
                                             style="cursor: pointer;"
                                             onclick="openImageModal(<?php echo e($loop->index); ?>)"
                                             data-image="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                             data-caption="<?php echo e($image->description ?? $service->title); ?>">
                                        <?php if($image->is_cover): ?>
                                        <div class="position-absolute top-0 end-0 m-1">
                                            <span class="badge bg-primary">รูปหลัก</span>
                                        </div>
                                        <?php endif; ?>
                                        <div class="position-absolute bottom-0 start-0 w-100 bg-dark bg-opacity-75 text-white p-1 gallery-thumb-caption" style="font-size: 0.75rem;">
                                            <?php echo e(Str::limit($image->description ?? 'รูปที่ ' . $loop->iteration, 20)); ?>

                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Content -->
                        <div class="content">
                            <h3 class="mb-3">รายละเอียด</h3>
                            <?php if($service->details): ?>
                                <div class="mb-4">
                                    <?php echo nl2br(e($service->details)); ?>

                                </div>
                            <?php else: ?>
                                <p class="text-muted mb-4"><?php echo e($service->description); ?></p>
                            <?php endif; ?>

                            <!-- Service Info -->
                            <?php if($service->category): ?>
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <?php if($service->category->icon): ?>
                                        <i class="<?php echo e($service->category->icon); ?> text-primary me-3 fa-lg"></i>
                                        <?php else: ?>
                                        <i class="fas fa-tags text-primary me-3 fa-lg"></i>
                                        <?php endif; ?>
                                        <div>
                                            <small class="text-muted d-block">หมวดหมู่</small>
                                            <strong><?php echo e($service->category->name); ?></strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <i class="fas fa-clock text-primary me-3 fa-lg"></i>
                                        <div>
                                            <small class="text-muted d-block">บริการ</small>
                                            <strong>ตลอด 24 ชั่วโมง</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Share Section -->
                            <div class="border-top pt-4">
                                <h5 class="mb-3">แชร์บริการนี้</h5>
                                <div class="d-flex gap-2">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(request()->fullUrl())); ?>"
                                       target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-facebook me-1"></i>Facebook
                                    </a>
                                    <a href="https://line.me/R/msg/text/?<?php echo e(urlencode($service->title . ' - ' . request()->fullUrl())); ?>"
                                       target="_blank" class="btn btn-outline-success btn-sm">
                                        <i class="fab fa-line me-1"></i>Line
                                    </a>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                                        <i class="fas fa-copy me-1"></i>คัดลอกลิงก์
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Contact CTA -->
                        <div class="bg-light p-4 rounded">
                            <div class="text-center">
                                <h4 class="fw-bold mb-3">สนใจบริการนี้?</h4>
                                <p class="mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก</p>
                                <div class="d-flex justify-content-center gap-3 flex-wrap">
                                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                                    </a>
                                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                                    </a>
                                </div>
                                <small class="text-muted d-block mt-3">
                                    <i class="fas fa-clock me-1"></i>
                                    บริการตลอด 24 ชั่วโมง
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Service Category -->
                <?php if($service->category): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">หมวดหมู่บริการ</h5>
                        <div class="d-flex align-items-center">
                            <?php if($service->category->icon): ?>
                            <i class="<?php echo e($service->category->icon); ?> fa-2x me-3" style="color: <?php echo e($service->category->color); ?>"></i>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-1"><?php echo e($service->category->name); ?></h6>
                                <?php if($service->category->description): ?>
                                <small class="text-muted"><?php echo e($service->category->description); ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Related Services -->
                <?php if($relatedServices->count() > 0): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">บริการที่เกี่ยวข้อง</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php $__currentLoopData = $relatedServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border-bottom p-3">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    <?php
                                        $relatedImage = $relatedService->images->where('is_cover', true)->first() ?? $relatedService->images->first();
                                        $relatedImagePath = $relatedImage ? $relatedImage->image_path : $relatedService->image;
                                    ?>
                                    <?php if($relatedImagePath): ?>
                                    <img src="<?php echo e(asset('storage/' . $relatedImagePath)); ?>" 
                                         class="rounded" 
                                         style="width: 60px; height: 60px; object-fit: cover;"
                                         alt="<?php echo e($relatedService->title); ?>">
                                    <?php else: ?>
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="<?php echo e(route('services.show', $relatedService->id)); ?>" 
                                           class="text-decoration-none"><?php echo e($relatedService->title); ?></a>
                                    </h6>
                                    <p class="text-muted small mb-0"><?php echo e(Str::limit($relatedService->description, 80)); ?></p>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Contact Card -->
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">ต้องการความช่วยเหลือ?</h5>
                        <p class="card-text">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก</p>
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                            </a>
                            <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                            </a>
                        </div>
                        <small class="text-muted d-block mt-2">
                            <i class="fas fa-clock me-1"></i>
                            บริการตลอด 24 ชั่วโมง
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="true" data-bs-keyboard="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e($service->title); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="modalCloseBtn"></button>
            </div>
            <div class="modal-body text-center p-0 position-relative">
                <?php
                    $modalMainImage = $service->images->where('is_cover', true)->first() ?? $service->images->first();
                    $modalMainImagePath = $modalMainImage ? $modalMainImage->image_path : $service->image;
                ?>
                <img src="<?php echo e(asset('storage/' . $modalMainImagePath)); ?>"
                     class="img-fluid w-100"
                     alt="<?php echo e($service->title); ?>"
                     id="modalImage"
                     style="max-height: 80vh; object-fit: contain;">

                <!-- Navigation arrows -->
                <?php if($service->images->count() > 1): ?>
                <button class="btn btn-dark btn-sm position-absolute top-50 start-0 translate-middle-y ms-3"
                        id="prevImageBtn" style="z-index: 10;">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="btn btn-dark btn-sm position-absolute top-50 end-0 translate-middle-y me-3"
                        id="nextImageBtn" style="z-index: 10;">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <?php endif; ?>
            </div>
            <?php if($service->images->count() > 1): ?>
            <div class="modal-footer justify-content-center">
                <div class="d-flex gap-2 flex-wrap" id="modalThumbnails">
                    <?php $__currentLoopData = $service->images->sortBy('sort_order'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="modal-thumb <?php echo e($index === 0 ? 'active' : ''); ?>"
                         style="width: 60px; height: 60px; cursor: pointer; border: 2px solid transparent; border-radius: 4px; overflow: hidden;"
                         data-index="<?php echo e($index); ?>"
                         data-image="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                         data-alt="<?php echo e($image->description ?? $service->title); ?>">
                        <img src="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                             class="img-thumbnail w-100 h-100"
                             style="object-fit: cover;"
                             alt="<?php echo e($image->description ?? $service->title); ?>">
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Service Image Gallery Management System (same as Activity)
class ServiceImageGallery {
    constructor() {
        this.currentImageIndex = 0;
        this.images = [];
        this.init();
    }

    init() {
        this.loadImages();
        this.setupEventListeners();
        this.setInitialActiveStates();
    }

    loadImages() {
        // Load all gallery images
        const galleryThumbnails = document.querySelectorAll('.gallery-thumbnail');
        galleryThumbnails.forEach((thumb, index) => {
            this.images.push({
                src: thumb.dataset.image,
                caption: thumb.dataset.caption || thumb.alt,
                element: thumb
            });
        });
    }

    setupEventListeners() {
        // Main image click to open modal
        const mainImage = document.getElementById('mainImage');
        if (mainImage) {
            mainImage.addEventListener('click', () => {
                this.openModal();
            });
        }

        // Gallery thumbnail clicks
        document.querySelectorAll('.gallery-thumbnail').forEach((thumb, index) => {
            thumb.addEventListener('click', (e) => {
                e.preventDefault();
                this.changeMainImage(index);
            });
        });

        // Modal thumbnail clicks
        document.querySelectorAll('.modal-thumb').forEach((thumb, index) => {
            thumb.addEventListener('click', (e) => {
                e.preventDefault();
                this.changeModalImage(index);
            });
        });

        // Navigation buttons
        const prevBtn = document.getElementById('prevImageBtn');
        const nextBtn = document.getElementById('nextImageBtn');

        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.previousImage());
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextImage());
        }

        // Keyboard navigation in modal
        document.addEventListener('keydown', (e) => {
            const modal = document.getElementById('imageModal');
            if (modal && modal.classList.contains('show')) {
                if (e.key === 'ArrowLeft') {
                    this.previousImage();
                } else if (e.key === 'ArrowRight') {
                    this.nextImage();
                } else if (e.key === 'Escape') {
                    this.closeModal();
                }
            }
        });
    }

    setInitialActiveStates() {
        // Set first thumbnail as active
        const firstThumbnail = document.querySelector('.gallery-thumbnail');
        if (firstThumbnail) {
            firstThumbnail.parentElement.classList.add('active-thumbnail');
        }

        // Set first modal thumbnail as active
        const firstModalThumb = document.querySelector('.modal-thumb');
        if (firstModalThumb) {
            this.setActiveModalThumb(firstModalThumb);
        }
    }

    openModal() {
        const modal = document.getElementById('imageModal');
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        this.updateModalImage();
    }

    closeModal() {
        const modal = document.getElementById('imageModal');
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
    }

    changeMainImage(index) {
        this.currentImageIndex = index;
        const mainImage = document.getElementById('mainImage');
        if (mainImage && this.images[index]) {
            mainImage.src = this.images[index].src;
            mainImage.alt = this.images[index].caption;
        }
        this.updateActiveThumbnail();
    }

    changeModalImage(index) {
        this.currentImageIndex = index;
        this.updateModalImage();
        this.updateActiveModalThumb();
    }

    updateModalImage() {
        const modalImage = document.getElementById('modalImage');
        if (modalImage && this.images[this.currentImageIndex]) {
            modalImage.src = this.images[this.currentImageIndex].src;
            modalImage.alt = this.images[this.currentImageIndex].caption;
        }
    }

    updateActiveThumbnail() {
        // Remove active class from all thumbnails
        document.querySelectorAll('.gallery-image-container').forEach(container => {
            container.classList.remove('active-thumbnail');
        });

        // Add active class to current thumbnail
        const currentThumb = document.querySelectorAll('.gallery-thumbnail')[this.currentImageIndex];
        if (currentThumb) {
            currentThumb.parentElement.classList.add('active-thumbnail');
        }
    }

    updateActiveModalThumb() {
        // Remove active class from all modal thumbnails
        document.querySelectorAll('.modal-thumb').forEach(thumb => {
            thumb.classList.remove('active');
            thumb.style.border = '2px solid transparent';
        });

        // Add active class to current modal thumbnail
        const currentModalThumb = document.querySelectorAll('.modal-thumb')[this.currentImageIndex];
        if (currentModalThumb) {
            this.setActiveModalThumb(currentModalThumb);
        }
    }

    setActiveModalThumb(thumb) {
        thumb.classList.add('active');
        thumb.style.border = '2px solid #007bff';
    }

    previousImage() {
        if (this.images.length > 0) {
            this.currentImageIndex = (this.currentImageIndex - 1 + this.images.length) % this.images.length;
            this.updateModalImage();
            this.updateActiveModalThumb();
        }
    }

    nextImage() {
        if (this.images.length > 0) {
            this.currentImageIndex = (this.currentImageIndex + 1) % this.images.length;
            this.updateModalImage();
            this.updateActiveModalThumb();
        }
    }
}

// Global functions for backward compatibility
function openImageModal(index) {
    if (window.serviceGallery) {
        window.serviceGallery.currentImageIndex = index;
        window.serviceGallery.openModal();
    }
}

function copyToClipboard() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>คัดลอกแล้ว';
        btn.classList.remove('btn-outline-secondary');
        btn.classList.add('btn-success');

        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.serviceGallery = new ServiceImageGallery();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/frontend/service-detail.blade.php ENDPATH**/ ?>