<?php $__env->startSection('title', $package->name . ' - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Banner Slider -->
<section class="hero-section position-relative <?php echo e($banners->count() === 0 ? 'hero-fallback' : ''); ?>">
    <?php if($banners->count() > 0): ?>
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="carousel-item <?php echo e($index === 0 ? 'active' : ''); ?>">
                    <div class="banner-slide" style="background-image: url('<?php echo e(asset('storage/' . $banner->image_path)); ?>');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if($banners->count() > 1): ?>
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="<?php echo e($index); ?>"
                        class="<?php echo e($index === 0 ? 'active' : ''); ?>" aria-current="true" aria-label="Slide <?php echo e($index + 1); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white"><?php echo e($package->name); ?></h1>
                    <p class="lead text-white">รายละเอียดแพคเกจบริการจัดงานศพ</p>
                    <nav aria-label="breadcrumb" class="mt-4">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white-50">หน้าหลัก</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('packages')); ?>" class="text-white-50">แพคเกจ</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e($package->name); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4"><?php echo e($package->name); ?></h1>
                    <p class="lead">รายละเอียดแพคเกจบริการจัดงานศพ</p>
                    <nav aria-label="breadcrumb" class="mt-4">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">หน้าหลัก</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('packages')); ?>">แพคเกจ</a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo e($package->name); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Package Detail Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8" data-aos="fade-right">
                <div class="card">
                    <div class="card-body p-4">
                        <!-- Main Image -->
                        <div class="mb-4">
                            <?php if($package->image && file_exists(storage_path('app/public/' . $package->image))): ?>
                            <div class="img-container-fixed img-size-xlarge rounded shadow-sm">
                                <img src="<?php echo e(asset('storage/' . $package->image)); ?>"
                                     class="img-fit-contain main-image"
                                     alt="<?php echo e($package->name); ?>"
                                     style="cursor: pointer;"
                                     data-bs-toggle="modal"
                                     data-bs-target="#imageModal"
                                     id="mainImage">
                            </div>
                            <?php else: ?>
                            <div class="bg-light d-flex align-items-center justify-content-center rounded shadow-sm"
                                 style="height: 400px;">
                                <div class="text-center text-muted">
                                    <i class="fas fa-box-open fa-5x mb-3"></i>
                                    <p class="mb-0">ไม่มีรูปภาพ</p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Package Description -->
                        <div class="mb-4">
                            <h3 class="fw-bold mb-3">รายละเอียดแพคเกจ</h3>
                            <div class="package-description">
                                <p class="lead"><?php echo e($package->description); ?></p>
                            </div>
                        </div>

                        <!-- Package Features -->
                        <?php if($package->features): ?>
                        <div class="mb-4">
                            <h3 class="fw-bold mb-3">
                                <i class="fas fa-list-check me-2"></i>รายการที่รวมอยู่ในแพคเกจ
                            </h3>
                            <div class="features-list bg-light p-4 rounded">
                                <?php
                                    $features = explode("\n", $package->features);
                                ?>
                                <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(trim($feature)): ?>
                                    <div class="d-flex align-items-start mb-2">
                                        <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                                        <span><?php echo e(trim($feature)); ?></span>
                                    </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Contact CTA -->
                        <div class="text-center p-4 bg-light rounded">
                            <h4 class="mb-3">สนใจแพคเกจนี้?</h4>
                            <p class="text-muted mb-4">ติดต่อเราเพื่อสอบถามรายละเอียดเพิ่มเติมและจองบริการ</p>
                            <div class="d-flex gap-3 justify-content-center flex-wrap">
                                <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-envelope me-2"></i>ติดต่อสอบถาม
                                </a>
                                <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-phone me-2"></i>โทรเลย
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4" data-aos="fade-left">
                <div class="card sticky-top" style="top: 100px;">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-box me-2"></i>ข้อมูลแพคเกจ
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Package Name -->
                        <h4 class="card-title"><?php echo e($package->name); ?></h4>

                        <!-- Featured Badge -->
                        <?php if($package->is_featured): ?>
                        <div class="mb-3">
                            <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                                <i class="fas fa-star me-2"></i>แพคเกจแนะนำ
                            </span>
                        </div>
                        <?php endif; ?>

                        <!-- Price -->
                        <?php if($package->price_text): ?>
                        <div class="mb-3">
                            <h6 class="text-muted">ราคา</h6>
                            <div class="price-display-large">
                                <span class="h3 text-success"><?php echo e($package->price_text); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Duration -->
                        <?php if($package->duration): ?>
                        <div class="mb-3">
                            <h6 class="text-muted">ระยะเวลา</h6>
                            <p class="mb-0">
                                <i class="fas fa-clock me-2 text-primary"></i><?php echo e($package->duration); ?>

                            </p>
                        </div>
                        <?php endif; ?>

                        <!-- Quick Contact -->
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>สอบถามแพคเกจ
                            </a>
                            <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? 'โทรสอบถาม'); ?>

                            </a>
                        </div>

                        <hr>

                        <!-- Package Info -->
                        <div class="small text-muted">
                            <div class="d-flex justify-content-between mb-2">
                                <span>สร้างเมื่อ:</span>
                                <span><?php echo e($package->created_at->format('d/m/Y')); ?></span>
                            </div>
                            <?php if(!$package->price_text): ?>
                            <div class="text-center mt-3">
                                <small class="text-info">
                                    <i class="fas fa-info-circle me-1"></i>
                                    สอบถามราคาได้ที่เจ้าหน้าที่
                                </small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Packages Section -->
<?php if($relatedPackages->count() > 0): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">แพคเกจอื่นๆ ที่น่าสนใจ</h2>
            <p class="section-subtitle">แพคเกจบริการอื่นๆ ที่อาจเหมาะสมกับความต้องการของคุณ</p>
        </div>

        <div class="row g-4">
            <?php $__currentLoopData = $relatedPackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedPackage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                <div class="card package-card h-100 <?php echo e($relatedPackage->is_featured ? 'border-warning' : ''); ?>">
                    <?php if($relatedPackage->is_featured): ?>
                    <div class="card-header bg-warning text-dark text-center fw-bold">
                        <i class="fas fa-star me-2"></i>แพคเกจแนะนำ
                    </div>
                    <?php endif; ?>
                    
                    <?php if($relatedPackage->image && file_exists(storage_path('app/public/' . $relatedPackage->image))): ?>
                    <img src="<?php echo e(asset('storage/' . $relatedPackage->image)); ?>" 
                         class="card-img-top" 
                         alt="<?php echo e($relatedPackage->name); ?>" 
                         style="height: 200px; object-fit: cover;">
                    <?php else: ?>
                    <div class="bg-light d-flex align-items-center justify-content-center" 
                         style="height: 200px;">
                        <i class="fas fa-box-open fa-3x text-muted"></i>
                    </div>
                    <?php endif; ?>

                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-3 flex-wrap">
                            <h5 class="card-title mb-0 me-2"><?php echo e($relatedPackage->name); ?></h5>
                            <?php if($relatedPackage->price_text): ?>
                            <div class="price-display">
                                <span class="small"><?php echo e($relatedPackage->price_text); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>

                        <p class="card-text"><?php echo e(Str::limit($relatedPackage->description, 100)); ?></p>

                        <div class="mt-auto">
                            <div class="d-flex gap-2">
                                <a href="<?php echo e(route('packages.show', $relatedPackage->id)); ?>" class="btn btn-outline-primary flex-fill">
                                    <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                                </a>
                                <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary flex-fill">
                                    <i class="fas fa-envelope me-2"></i>สอบถาม
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-5">
            <a href="<?php echo e(route('packages')); ?>" class="btn btn-primary btn-lg">ดูแพคเกจทั้งหมด</a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการตลอด 24 ชั่วโมง ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel"><?php echo e($package->name); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <?php if($package->image && file_exists(storage_path('app/public/' . $package->image))): ?>
                <img src="<?php echo e(asset('storage/' . $package->image)); ?>"
                     class="img-fluid rounded"
                     alt="<?php echo e($package->name); ?>"
                     id="modalImage">
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->startSection('styles'); ?>
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<style>
/* Image Container - เหมือน Service */
.img-container-fixed {
    position: relative;
    overflow: hidden;
    background: #f8f9fa;
}

.img-size-xlarge {
    height: 400px;
}

.img-fit-contain {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.main-image:hover {
    transform: scale(1.05);
}

/* Features List */
.features-list {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid var(--bs-primary);
}

.features-list .fas.fa-check-circle {
    font-size: 1.1rem;
}

/* Price Display */
.price-display-large {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    display: inline-block;
}

/* Sidebar Sticky */
.sticky-top {
    position: sticky;
    top: 100px;
    z-index: 1020;
}

/* Card Hover Effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

@media (max-width: 991px) {
    .sticky-top {
        position: relative;
        top: auto;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/frontend/package-detail.blade.php ENDPATH**/ ?>